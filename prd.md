



# 文件目录

```
ai-backend/
├─ app/
│  ├─ main.py                    # FastAPI 入口，挂载各模块路由
│  ├─ core/
│  │  ├─ config.py               # 配置/环境变量加载（pydantic-settings）
│  │  ├─ logging.py              # 统一日志（loguru）
│  │  └─ deps.py                 # 依赖注入/统一客户端（OpenAI/LlamaIndex等）
│  ├─ api/
│  │  └─ v1/
│  │     ├─ graphrag.py          # 模块1：接收 txt => 生成知识图谱 Parquet
│  │     ├─ outline.py           # 模块2：OpenAI 抽取文档大纲 => md
│  │     └─ rag.py               # 模块3：LlamaIndex RAG 问答
│  ├─ schemas/
│  │  ├─ graphrag.py             # 请求/响应模型（上传txt、返回任务ID/文件路径等）
│  │  ├─ outline.py
│  │  └─ rag.py
│  ├─ services/
│  │  ├─ graphrag_service.py     # 业务逻辑：GraphRAG 索引与导出
│  │  ├─ outline_service.py      # 业务逻辑：OpenAI 调用 & 产出 md
│  │  └─ rag_service.py          # 业务逻辑：LlamaIndex 索引/检索/问答
│  ├─ repositories/
│  │  └─ storage.py              # 本地/对象存储抽象（生成的 parquet/md 的落盘/签名URL等）
│  ├─ workers/
│  │  └─ background.py           # 可选：长任务后台执行（Celery/RQ/内置 BackgroundTasks）
│  ├─ utils/
│  │  ├─ fileio.py               # 安全保存上传 txt、清理 tmp
│  │  ├─ idgen.py                # 统一任务/文件名生成
│  │  └─ timers.py               # 超时/度量
│  └─ constants/
│     └─ paths.py                # 统一的目录常量（/data/parquet 等）
├─ data/
│  ├─ uploads/                   # 前端上传的 txt 临时区
│  ├─ outputs/
│  │  ├─ graphrag/               # 产出的 parquet/graph 相关文件
│  │  ├─ outlines/               # 产出的 md
│  │  └─ rag/                    # 可选：缓存/索引工件
│  └─ tmp/
├─ tests/
│  ├─ test_graphrag.py
│  ├─ test_outline.py
│  └─ test_rag.py
├─ scripts/
│  ├─ dev_run.sh                 # 本地启动/热重载
│  └─ build_index.py             # 可选：离线批量索引脚本
├─ .env.example                  # 环境变量样例（OPENAI_API_KEY 等）
├─ requirements.txt
├─ requirements-optional.txt     # 可选：Qdrant/Bedrock等扩展
├─ README.md
└─ pyproject.toml                # 可选：统一工具链配置（ruff/black/mypy）
```



# requirements.txt

```
` # --- Web & schema ---
fastapi==0.116.1
uvicorn[standard]==0.30.6
pydantic==2.8.2
pydantic-settings==2.4.0
starlette==0.47.2


```

````
 哈哈！在 Python 中，定义函数的基本语法如下：

```python
def function_name(parameters):
    """函数的文档字符串，描述函数的功能。"""
    # 函数体
    return value  # 可选的返回值
```

### 示例

下面是一个简单的函数示例，它接受两个参数并返回它们的和：

```python
def add_numbers(a, b):
    """返回两个数字的和。"""
    return a + b
```

在这个示例中，`add_numbers` 是函数名，`a` 和 `b` 是参数，函数体中返回了这两个参数的和。
````
