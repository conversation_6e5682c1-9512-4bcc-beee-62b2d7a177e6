# AI 功能后端开发计划 - "文本生成大纲"模块

## 1. 项目概述与目标

### 1.1 项目范围

- 基于 FastAPI 构建 AI 功能后端
- 重点实现"文本生成大纲"模块
- 为后续 RAG 问答和 GraphRAG 模块做好架构准备

### 1.2 核心功能

- 接收 Markdown 文件上传
- 使用 OpenAI API 生成三层次结构化大纲
- 返回 Markdown 格式的大纲内容和文件路径

## 2. 技术架构设计

### 2.1 技术栈选择

- **Web 框架**: FastAPI 0.116.1 (异步支持，自动文档生成)
- **Python 版本**: 3.11 (兼容性最佳)
- **AI 服务**: OpenAI API (gpt-4o-mini 模型)
- **文件处理**: aiofiles (异步文件操作)
- **配置管理**: pydantic-settings (类型安全的配置)
- **日志**: loguru (结构化日志)

### 2.2 项目结构设计考虑

- 采用分层架构：API 层 → Service 层 → Repository 层
- 模块化设计，便于后续扩展 RAG 和 GraphRAG 功能
- 统一的错误处理和响应格式
- 配置文件与代码分离

## 3. 详细实施步骤

### 3.1 环境准备阶段

#### 步骤 1: 创建项目目录结构

- 按照文档规范创建完整的目录树
- 建立 `ai-backend/` 根目录
- 创建 `app/`、`data/`、`scripts/`、`tests/` 等主要目录
- 设置 `data/uploads/`、`data/outputs/outlines/` 等存储目录

#### 步骤 2: 虚拟环境与依赖管理

- 创建 Python 3.11 虚拟环境
- 安装核心依赖：FastAPI、uvicorn、openai、aiofiles 等
- 创建 `requirements.txt` 和 `requirements-optional.txt`
- 配置 `pyproject.toml` 用于项目元数据管理

#### 步骤 3: 环境变量配置

- 创建 `.env.example` 模板文件
- 配置 OpenAI API 相关环境变量
- 设置文件路径和服务端口等配置项
- 建立配置验证机制

### 3.2 核心模块开发阶段

#### 步骤 4: 配置管理模块 (`app/core/`)

- 实现 `config.py`：使用 pydantic-settings 管理配置
- 实现 `logging.py`：配置 loguru 日志系统
- 实现 `deps.py`：依赖注入和公共依赖项
- 配置项包括：API 密钥、模型名称、文件路径、服务配置等

#### 步骤 5: 数据模型定义 (`app/schemas/`)

- 定义 `outline.py` 中的请求/响应模型
- 文件上传请求模型：支持文件元数据
- 大纲生成响应模型：包含生成的内容和文件路径
- 错误响应模型：统一的错误格式

#### 步骤 6: 业务逻辑层 (`app/services/`)

- 实现 `outline_service.py` 核心业务逻辑
- 文件验证：检查文件格式、大小限制
- 内容提取：读取 Markdown 文件内容
- AI 调用：集成 OpenAI API，使用 notebook 中的提示词
- 结果处理：格式化输出，保存生成的大纲文件

#### 步骤 7: 工具函数模块 (`app/utils/`)

- 实现 `fileio.py`：文件读写、路径处理、文件验证
- 实现 `idgen.py`：生成唯一文件名和任务 ID
- 实现 `timers.py`：时间戳处理和性能监控
- 文件安全检查：防止路径遍历攻击

#### 步骤 8: API 路由层 (`app/api/v1/`)

- 实现 `outline.py` API 端点
- POST `/api/v1/outline/generate`：文件上传和大纲生成
- GET `/api/v1/outline/{task_id}`：查询生成状态和结果
- 文件上传处理：支持 multipart/form-data
- 异步处理：避免长时间请求阻塞

### 3.3 应用入口与配置

#### 步骤 9: 主应用文件 (`app/main.py`)

- FastAPI 应用初始化
- 中间件配置：CORS、请求日志、异常处理
- 路由注册：挂载 v1 API 路由
- 启动事件：初始化检查、目录创建
- 优雅关闭：资源清理

#### 步骤 10: 常量和路径管理 (`app/constants/`)

- 实现 `paths.py`：统一管理文件路径常量
- 定义上传目录、输出目录、临时目录路径
- 文件扩展名白名单
- 大小限制等常量定义

### 3.4 测试与验证阶段

#### 步骤 11: 单元测试 (`tests/`)

- 实现 `test_outline.py`：测试大纲生成功能
- 模拟文件上传测试
- AI API 调用的 mock 测试
- 错误处理测试：无效文件、API 失败等场景
- 使用 pytest 和 httpx 进行异步测试

#### 步骤 12: 集成测试

- 端到端测试：完整的文件上传到大纲生成流程
- API 文档测试：验证 OpenAPI 规范
- 性能测试：大文件处理、并发请求
- 错误恢复测试：网络异常、磁盘空间不足等

## 4. 关键技术注意事项

### 4.1 安全考虑

- **文件上传安全**：限制文件类型、大小，防止恶意文件
- **路径安全**：防止目录遍历攻击，使用安全的文件名生成
- **API 密钥保护**：环境变量存储，不在日志中暴露
- **输入验证**：严格验证所有用户输入

### 4.2 性能优化

- **异步处理**：使用 FastAPI 的异步特性处理 I/O 操作
- **文件流处理**：大文件分块读取，避免内存溢出
- **缓存策略**：考虑对相同内容的大纲生成结果进行缓存
- **资源限制**：设置合理的文件大小和请求超时限制

### 4.3 错误处理

- **分层错误处理**：API 层、Service 层、工具层的错误传递
- **用户友好错误**：将技术错误转换为用户可理解的消息
- **日志记录**：详细记录错误上下文，便于调试
- **重试机制**：对 AI API 调用实现指数退避重试

### 4.4 可扩展性设计

- **模块化架构**：为后续 RAG 和 GraphRAG 功能预留接口
- **配置驱动**：通过配置文件控制功能开关和参数
- **插件化设计**：AI 服务提供商可替换（OpenAI → 其他）
- **数据库准备**：虽然当前使用文件存储，但预留数据库接口

### 4.5 提示词工程

- **模板化管理**：将 notebook 中的提示词提取为可配置模板
- **参数化设计**：支持动态调整提示词参数
- **版本控制**：提示词变更的版本管理
- **A/B 测试**：支持多个提示词版本的效果对比

## 5. 开发里程碑

### 里程碑 1: 基础架构搭建

- 完成项目结构创建
- 环境配置和依赖安装
- 基础配置模块实现

### 里程碑 2: 核心功能实现

- 文件上传和处理逻辑
- AI API 集成和提示词实现
- 大纲生成和保存功能

### 里程碑 3: API 接口完善

- REST API 端点实现
- 请求/响应模型定义
- 错误处理和验证

### 里程碑 4: 测试和优化

- 单元测试和集成测试
- 性能优化和安全加固
- 文档完善

## 6. 风险评估与应对

### 6.1 技术风险

- **AI API 稳定性**：实现重试机制和降级策略
- **文件处理复杂性**：充分测试各种 Markdown 格式
- **并发处理**：合理设计异步处理和资源管理

### 6.2 业务风险

- **提示词效果**：建立提示词测试和优化流程
- **用户体验**：设计友好的错误提示和进度反馈
- **扩展性**：确保架构能支持后续功能模块

## 7. 提示词实现细节

### 7.1 基于 Notebook 的提示词设计

根据 `final_md2mindmap.ipynb` 中的实现，提示词分为两个部分：

#### 第一阶段：生成原始大纲

- 使用 `text_to_raw_md_prompt_header` + 文档内容 + `text_to_raw_md_prompt_footer`
- 模型：gpt-4.1 (可配置为 gpt-4o-mini)
- 输出：二级和三级标题的结构化 Markdown

#### 第二阶段：精简标题

- 对第一阶段的输出进行标题精简
- 生成合适的一级标题
- 将二级标题替换为简洁短语
- 保持三级标题不变

### 7.2 提示词模板化

- 将提示词提取为可配置的模板文件
- 支持参数化替换（如文档类型、输出格式等）
- 版本管理和 A/B 测试支持

## 8. 部署和运维考虑

### 8.1 容器化部署

- 创建 Dockerfile 用于容器化部署
- 配置 docker-compose.yml 用于本地开发
- 环境变量和配置文件的容器化管理

### 8.2 监控和日志

- 集成应用性能监控（APM）
- 结构化日志输出，便于日志聚合
- 健康检查端点和指标暴露

### 8.3 扩展性准备

- 支持水平扩展的无状态设计
- 文件存储可替换为对象存储（如 S3）
- 数据库连接池和缓存层预留

---

**注意**: 这个计划专注于"文本生成大纲"模块的实现，同时为后续的 RAG 问答和 GraphRAG 模块预留了扩展空间。每个步骤都有明确的技术要求和实现细节，确保项目能够高质量完成。
